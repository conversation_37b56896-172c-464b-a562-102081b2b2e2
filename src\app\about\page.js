'use client';

import { motion } from 'framer-motion';
import { Download, Calendar, MapPin, Mail } from 'lucide-react';
import Image from 'next/image';
import { personalInfo, skills, experience } from '@/data/portfolio';

export default function About() {
  return (
    <div className="min-h-screen py-20 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl sm:text-5xl font-bold text-black mb-6">
            About Me
          </h1>
          <p className="text-xl text-mono-gray-600 max-w-3xl mx-auto">
            Get to know more about my background, skills, and passion for creating exceptional web experiences.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Profile Section */}
          <motion.div
            initial={{ x: -30, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="lg:col-span-1"
          >
            <div className="bg-white border border-mono-gray-200 rounded-2xl shadow-xl p-8 text-center">
              <div className="w-48 h-48 mx-auto rounded-full overflow-hidden border-4 border-mono-gray-200 shadow-lg mb-6">
                <Image
                  src="/profile.jpg"
                  alt={personalInfo.name}
                  width={192}
                  height={192}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(personalInfo.name)}&size=192&background=000000&color=fff`;
                  }}
                />
              </div>

              <h2 className="text-2xl font-bold text-black mb-2">
                {personalInfo.name}
              </h2>
              <p className="text-primary-600 font-medium mb-6">
                {personalInfo.title}
              </p>
              
              <div className="space-y-3 text-sm text-mono-gray-600 mb-8">
                <div className="flex items-center justify-center space-x-2">
                  <MapPin size={16} />
                  <span>{personalInfo.location}</span>
                </div>
                <div className="flex items-center justify-center space-x-2">
                  <Mail size={16} />
                  <span>{personalInfo.email}</span>
                </div>
              </div>
              
              <a
                href={personalInfo.resume}
                download
                className="inline-flex items-center px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl w-full justify-center"
              >
                <Download size={18} className="mr-2" />
                Download Resume
              </a>
            </div>
          </motion.div>

          {/* Content Section */}
          <motion.div
            initial={{ x: 30, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="lg:col-span-2 space-y-12"
          >
            {/* Bio */}
            <div>
              <h3 className="text-2xl font-bold text-black mb-6">
                My Story
              </h3>
              <div className="prose prose-lg max-w-none">
                <p className="text-mono-gray-600 leading-relaxed mb-4">
                  {personalInfo.bio}
                </p>
                <p className="text-mono-gray-600 leading-relaxed mb-4">
                  With a passion for both frontend and backend development, I enjoy creating
                  complete web solutions that not only look great but also perform exceptionally.
                  I believe in writing clean, maintainable code and staying up-to-date with the
                  latest technologies and best practices.
                </p>
                <p className="text-mono-gray-600 leading-relaxed">
                  When I'm not coding, you can find me exploring new technologies, contributing
                  to open-source projects, or sharing my knowledge with the developer community.
                  I'm always excited to take on new challenges and collaborate on innovative projects.
                </p>
              </div>
            </div>

            {/* Experience */}
            <div>
              <h3 className="text-2xl font-bold text-black mb-6">
                Experience
              </h3>
              <div className="space-y-6">
                {experience.map((exp, index) => (
                  <motion.div
                    key={index}
                    initial={{ y: 20, opacity: 0 }}
                    whileInView={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="bg-white rounded-xl shadow-lg p-6 border border-mono-gray-200"
                  >
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3">
                      <h4 className="text-lg font-semibold text-black">
                        {exp.position}
                      </h4>
                      <div className="flex items-center text-sm text-mono-gray-500 mt-1 sm:mt-0">
                        <Calendar size={14} className="mr-1" />
                        {exp.duration}
                      </div>
                    </div>
                    <p className="text-primary-600 font-medium mb-3">
                      {exp.company}
                    </p>
                    <p className="text-mono-gray-600 mb-4">
                      {exp.description}
                    </p>
                    <div className="space-y-1">
                      {exp.achievements.map((achievement, achIndex) => (
                        <div key={achIndex} className="flex items-start space-x-2">
                          <div className="w-1.5 h-1.5 bg-primary-600 rounded-full mt-2 flex-shrink-0" />
                          <span className="text-sm text-mono-gray-600">
                            {achievement}
                          </span>
                        </div>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>

        {/* Skills Section */}
        <motion.div
          initial={{ y: 30, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="mt-20"
        >
          <h3 className="text-3xl font-bold text-black text-center mb-12">
            Skills & Technologies
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {Object.entries(skills).map(([category, skillList], index) => (
              <motion.div
                key={category}
                initial={{ y: 30, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white rounded-xl shadow-lg p-6 border border-mono-gray-200"
              >
                <h4 className="text-lg font-semibold text-black mb-4 capitalize">
                  {category}
                </h4>
                <div className="flex flex-wrap gap-2">
                  {skillList.map((skill, skillIndex) => (
                    <span
                      key={skillIndex}
                      className="px-3 py-1 bg-primary-100 text-primary-700 text-sm rounded-full font-medium"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
}
