# Portfolio Assets

This folder contains static assets for your portfolio website.

## Required Images

### Profile Image
- **File**: `profile.jpg`
- **Size**: 400x400px (minimum)
- **Format**: JPG, PNG, or WebP
- **Description**: Your professional headshot

### Project Images
Add project screenshots to the `projects/` folder:

- `ecommerce.jpg` - E-Commerce Platform screenshot
- `taskmanager.jpg` - Task Management App screenshot  
- `weather.jpg` - Weather Dashboard screenshot
- `analytics.jpg` - Social Media Analytics screenshot

**Recommended size**: 600x400px
**Format**: JPG, PNG, or WebP

### Resume
- **File**: `resume.pdf`
- **Description**: Your downloadable resume/CV

## Fallback Images

If you don't add custom images, the portfolio will automatically use:
- Generated avatar for profile image
- Placeholder images for projects

## Image Optimization

For best performance:
- Use WebP format when possible
- Optimize images before uploading
- Keep file sizes under 500KB
- Use descriptive filenames

## Adding Your Images

1. Replace the placeholder content in `/src/data/portfolio.js` with your information
2. Add your images to this `public/` folder
3. Update the image paths in the data file if needed
4. Test the portfolio to ensure all images load correctly
