import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/contexts/ThemeContext";
import Navigation from "@/components/layout/Navigation";
import Footer from "@/components/layout/Footer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: "Portfolio | Fullstack Web Developer",
  description: "Professional portfolio showcasing fullstack web development projects and skills in React, Next.js, Node.js, and modern web technologies.",
  keywords: "fullstack developer, web developer, React, Next.js, Node.js, portfolio",
  authors: [{ name: "Your Name" }],
  creator: "Your Name",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://yourportfolio.com",
    title: "Portfolio | Fullstack Web Developer",
    description: "Professional portfolio showcasing fullstack web development projects and skills.",
    siteName: "Portfolio",
  },
  twitter: {
    card: "summary_large_image",
    title: "Portfolio | Fullstack Web Developer",
    description: "Professional portfolio showcasing fullstack web development projects and skills.",
    creator: "@yourusername",
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-white text-black`}
      >
        <ThemeProvider>
          <div className="min-h-screen flex flex-col bg-white">
            <Navigation />
            <main className="flex-grow pt-16 bg-white">
              {children}
            </main>
            <Footer />
          </div>
        </ThemeProvider>
      </body>
    </html>
  );
}
