'use client';

import { motion } from 'framer-motion';
import { ExternalLink, Github, Tag } from 'lucide-react';
import Image from 'next/image';

export default function ProjectCard({ project, index = 0 }) {
  return (
    <motion.div
      initial={{ y: 30, opacity: 0 }}
      whileInView={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      viewport={{ once: true }}
      className="bg-white rounded-2xl shadow-xl overflow-hidden border border-mono-gray-200 hover:shadow-2xl transition-all duration-300"
    >
      {/* Project Image */}
      <div className="relative h-64 overflow-hidden">
        <Image
          src={project.image}
          alt={project.title}
          fill
          className="object-cover transition-transform duration-300 hover:scale-105"
          onError={(e) => {
            e.target.src = `https://via.placeholder.com/600x400/000000/ffffff?text=${encodeURIComponent(project.title)}`;
          }}
        />
        <div className="absolute top-4 right-4">
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${
            project.status === 'Completed'
              ? 'bg-green-100 text-green-800'
              : 'bg-yellow-100 text-yellow-800'
          }`}>
            {project.status}
          </span>
        </div>
      </div>

      {/* Project Content */}
      <div className="p-8">
        <h3 className="text-2xl font-bold text-black mb-3">
          {project.title}
        </h3>

        <p className="text-mono-gray-600 mb-6 leading-relaxed">
          {project.description}
        </p>

        {/* Technologies */}
        <div className="mb-6">
          <div className="flex items-center mb-3">
            <Tag size={16} className="text-mono-gray-500 mr-2" />
            <span className="text-sm font-medium text-mono-gray-700">
              Technologies
            </span>
          </div>
          <div className="flex flex-wrap gap-2">
            {project.technologies.map((tech, techIndex) => (
              <span
                key={techIndex}
                className="px-3 py-1 bg-primary-100 text-primary-700 text-sm rounded-full font-medium"
              >
                {tech}
              </span>
            ))}
          </div>
        </div>

        {/* Features */}
        <div className="mb-8">
          <h4 className="text-sm font-medium text-mono-gray-700 mb-3">
            Key Features:
          </h4>
          <ul className="space-y-2">
            {project.features.slice(0, 3).map((feature, featureIndex) => (
              <li key={featureIndex} className="flex items-start space-x-2">
                <div className="w-1.5 h-1.5 bg-primary-600 rounded-full mt-2 flex-shrink-0" />
                <span className="text-sm text-mono-gray-600">
                  {feature}
                </span>
              </li>
            ))}
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-4">
          <motion.a
            href={project.liveUrl}
            target="_blank"
            rel="noopener noreferrer"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex-1 inline-flex items-center justify-center px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            <ExternalLink size={16} className="mr-2" />
            Live Demo
          </motion.a>
          
          <motion.a
            href={project.githubUrl}
            target="_blank"
            rel="noopener noreferrer"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex-1 inline-flex items-center justify-center px-4 py-2 border-2 border-primary-600 text-primary-600 dark:text-primary-400 hover:bg-primary-600 hover:text-white font-medium rounded-lg transition-all duration-200"
          >
            <Github size={16} className="mr-2" />
            Code
          </motion.a>
        </div>
      </div>
    </motion.div>
  );
}
