// Portfolio data structure
export const personalInfo = {
  name: "Your Name",
  title: "Fullstack Web Developer",
  email: "<EMAIL>",
  phone: "+****************",
  location: "Your City, Country",
  bio: "Passionate fullstack developer with expertise in modern web technologies. I create scalable, user-friendly applications that solve real-world problems.",
  resume: "/resume.pdf", // Add your resume file to public folder
  image: "/profile.jpg", // Add your profile image to public folder
};

export const socialLinks = [
  {
    name: "GitHub",
    url: "https://github.com/yourusername",
    icon: "Github",
  },
  {
    name: "LinkedIn",
    url: "https://linkedin.com/in/yourusername",
    icon: "Linkedin",
  },
  {
    name: "Twitter",
    url: "https://twitter.com/yourusername",
    icon: "Twitter",
  },
  {
    name: "Email",
    url: "mailto:<EMAIL>",
    icon: "Mail",
  },
];

export const skills = {
  frontend: [
    "React",
    "Next.js",
    "Vue.js",
    "JavaScript",
    "TypeScript",
    "HTML5",
    "CSS3",
    "Tailwind CSS",
    "Sass",
    "Responsive Design",
  ],
  backend: [
    "Node.js",
    "Express.js",
    "Python",
    "Django",
    "Flask",
    "RESTful APIs",
    "GraphQL",
    "Microservices",
  ],
  database: [
    "MongoDB",
    "PostgreSQL",
    "MySQL",
    "Redis",
    "Firebase",
  ],
  tools: [
    "Git",
    "Docker",
    "AWS",
    "Vercel",
    "Netlify",
    "Jest",
    "Cypress",
    "Webpack",
    "Vite",
  ],
};

export const projects = [
  {
    id: 1,
    title: "E-Commerce Platform",
    description: "A full-featured e-commerce platform with user authentication, payment processing, and admin dashboard. Built with modern technologies for optimal performance.",
    image: "/projects/ecommerce.jpg", // Add project images to public/projects folder
    technologies: ["Next.js", "Node.js", "MongoDB", "Stripe", "Tailwind CSS"],
    features: [
      "User authentication and authorization",
      "Shopping cart and checkout process",
      "Payment integration with Stripe",
      "Admin dashboard for inventory management",
      "Responsive design for all devices",
    ],
    liveUrl: "https://your-ecommerce-demo.vercel.app",
    githubUrl: "https://github.com/yourusername/ecommerce-platform",
    status: "Completed",
  },
  {
    id: 2,
    title: "Task Management App",
    description: "A collaborative task management application with real-time updates, team collaboration features, and intuitive drag-and-drop interface.",
    image: "/projects/taskmanager.jpg",
    technologies: ["React", "Express.js", "PostgreSQL", "Socket.io", "Material-UI"],
    features: [
      "Real-time collaboration",
      "Drag and drop task management",
      "Team member invitations",
      "Progress tracking and analytics",
      "Mobile-responsive design",
    ],
    liveUrl: "https://your-taskmanager-demo.vercel.app",
    githubUrl: "https://github.com/yourusername/task-manager",
    status: "Completed",
  },
  {
    id: 3,
    title: "Weather Dashboard",
    description: "A comprehensive weather dashboard with location-based forecasts, interactive maps, and detailed weather analytics using multiple APIs.",
    image: "/projects/weather.jpg",
    technologies: ["Vue.js", "Python", "Flask", "OpenWeather API", "Chart.js"],
    features: [
      "Current weather and 7-day forecast",
      "Interactive weather maps",
      "Location-based weather alerts",
      "Historical weather data",
      "Responsive charts and graphs",
    ],
    liveUrl: "https://your-weather-demo.vercel.app",
    githubUrl: "https://github.com/yourusername/weather-dashboard",
    status: "Completed",
  },
  {
    id: 4,
    title: "Social Media Analytics",
    description: "A powerful analytics platform for social media management with data visualization, scheduling tools, and performance insights.",
    image: "/projects/analytics.jpg",
    technologies: ["Next.js", "Node.js", "MongoDB", "D3.js", "Redis"],
    features: [
      "Multi-platform social media integration",
      "Advanced data visualization",
      "Content scheduling and automation",
      "Performance analytics and reporting",
      "Real-time monitoring dashboard",
    ],
    liveUrl: "https://your-analytics-demo.vercel.app",
    githubUrl: "https://github.com/yourusername/social-analytics",
    status: "In Progress",
  },
];

export const experience = [
  {
    company: "Tech Company Inc.",
    position: "Senior Fullstack Developer",
    duration: "2022 - Present",
    description: "Lead development of scalable web applications using React, Node.js, and cloud technologies. Mentor junior developers and collaborate with cross-functional teams.",
    achievements: [
      "Improved application performance by 40%",
      "Led a team of 5 developers",
      "Implemented CI/CD pipelines",
    ],
  },
  {
    company: "Startup Solutions",
    position: "Fullstack Developer",
    duration: "2020 - 2022",
    description: "Developed and maintained multiple client projects using modern web technologies. Worked closely with designers and product managers to deliver high-quality solutions.",
    achievements: [
      "Delivered 15+ successful projects",
      "Reduced development time by 30%",
      "Implemented responsive design standards",
    ],
  },
];
